package cn.iocoder.yudao.module.wbclass.convert.course;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.module.wbclass.controller.app.course.vo.AppWbClassCourseCategoryRespVO;
import cn.iocoder.yudao.module.wbclass.controller.app.course.vo.AppWbClassCourseRespVO;
import cn.iocoder.yudao.module.wbclass.controller.app.course.vo.AppWbClassCourseSeriesDetailRespVO;
import cn.iocoder.yudao.module.wbclass.controller.app.course.vo.AppWbClassCourseSeriesRespVO;
import cn.iocoder.yudao.module.wbclass.controller.app.course.vo.AppWbClassUserCourseSeriesRespVO;
import cn.iocoder.yudao.module.wbclass.dal.dataobject.course.WbClassCourseDO;
import cn.iocoder.yudao.module.wbclass.dal.dataobject.courselesson.WbClassCourseLessonDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 课程系列 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface WbClassCourseSeriesConvert {

    WbClassCourseSeriesConvert INSTANCE = Mappers.getMapper(WbClassCourseSeriesConvert.class);

    /**
     * 将分组的课程数据转换为用户课程系列响应VO
     *
     * @param groupedCourses 按系列分组的课程数据
     * @return 用户课程系列响应VO
     */
    default AppWbClassUserCourseSeriesRespVO convertToUserCourseSeries(Map<String, List<WbClassCourseDO>> groupedCourses) {
        if (CollUtil.isEmpty(groupedCourses)) {
            return new AppWbClassUserCourseSeriesRespVO();
        }

        AppWbClassUserCourseSeriesRespVO result = new AppWbClassUserCourseSeriesRespVO();
        List<AppWbClassCourseSeriesRespVO> courseSeries = new ArrayList<>();
        List<AppWbClassCourseRespVO> individualCourses = new ArrayList<>();

        groupedCourses.forEach((seriesName, courses) -> {
            if ("individual_courses".equals(seriesName)) {
                // 处理单独的课程（不属于任何系列）
                individualCourses.addAll(WbClassCourseConvert.INSTANCE.convertAppList(courses));
            } else {
                // 处理系列课程
                AppWbClassCourseSeriesRespVO seriesVO = new AppWbClassCourseSeriesRespVO();
                seriesVO.setSeriesName(seriesName);
                
                List<AppWbClassCourseRespVO> courseVOs = WbClassCourseConvert.INSTANCE.convertAppList(courses);
                seriesVO.setAllCourses(courseVOs);
                
                // 设置默认课程（第一个，因为已经按seriesOrder排序）
                if (CollUtil.isNotEmpty(courseVOs)) {
                    seriesVO.setDefaultCourse(courseVOs.get(0));
                }
                
                courseSeries.add(seriesVO);
            }
        });

        result.setCourseSeries(courseSeries);
        result.setIndividualCourses(individualCourses);
        
        return result;
    }

    /**
     * 转换为课程分类列表（新的分层结构）
     */
    default List<AppWbClassCourseCategoryRespVO> convertToCategoryList(Map<String, List<WbClassCourseDO>> groupedCourses) {
        List<AppWbClassCourseCategoryRespVO> categories = new ArrayList<>();

        // 添加"我的作业"分类
        AppWbClassCourseCategoryRespVO homeworkCategory = new AppWbClassCourseCategoryRespVO();
        homeworkCategory.setId(0L);
        homeworkCategory.setName("我的作业");
        homeworkCategory.setType("homework");
        homeworkCategory.setSort(0);
        categories.add(homeworkCategory);

        if (CollUtil.isEmpty(groupedCourses)) {
            return categories;
        }

        int sortIndex = 1;

        for (Map.Entry<String, List<WbClassCourseDO>> entry : groupedCourses.entrySet()) {
            String seriesName = entry.getKey();
            List<WbClassCourseDO> courses = entry.getValue();

            if ("individual_courses".equals(seriesName)) {
                // 单独的课程（没有系列）
                for (WbClassCourseDO course : courses) {
                    AppWbClassCourseCategoryRespVO category = new AppWbClassCourseCategoryRespVO();
                    category.setId(1000L + course.getId()); // 使用1000+courseId作为分类ID
                    category.setName(course.getName());
                    category.setType("individual");
                    category.setSort(sortIndex++);
                    categories.add(category);
                }
            } else {
                // 系列课程
                AppWbClassCourseCategoryRespVO category = new AppWbClassCourseCategoryRespVO();
                category.setId((long) sortIndex); // 使用递增ID作为系列ID
                category.setName(seriesName);
                category.setType("series");
                category.setSeriesName(seriesName);
                category.setSort(sortIndex++);
                categories.add(category);
            }
        }

        return categories;
    }

    /**
     * 转换为系列详情
     */
    default AppWbClassCourseSeriesDetailRespVO convertToSeriesDetail(String seriesName, List<WbClassCourseDO> courses, Long seriesId) {
        if (CollUtil.isEmpty(courses)) {
            return null;
        }

        AppWbClassCourseSeriesDetailRespVO result = new AppWbClassCourseSeriesDetailRespVO();
        result.setSeriesId(seriesId);
        result.setSeriesName(seriesName);

        // 不进行排序，由调用方负责排序

        // 转换课程信息
        List<AppWbClassCourseSeriesDetailRespVO.CourseInfo> courseInfos = new ArrayList<>();
        for (WbClassCourseDO course : courses) {
            AppWbClassCourseSeriesDetailRespVO.CourseInfo courseInfo = convertToDetailCourseInfo(course);
            if (courseInfo != null) {
                courseInfos.add(courseInfo);
            }
        }

        result.setCourses(courseInfos);

        return result;
    }

    /**
     * 转换为详情课程信息
     */
    default AppWbClassCourseSeriesDetailRespVO.CourseInfo convertToDetailCourseInfo(WbClassCourseDO course) {
        if (course == null) {
            return null;
        }

        AppWbClassCourseSeriesDetailRespVO.CourseInfo courseInfo = new AppWbClassCourseSeriesDetailRespVO.CourseInfo();
        courseInfo.setId(course.getId());
        courseInfo.setName(course.getName());
        courseInfo.setVolumeName(course.getVolumeName());
        courseInfo.setCoverUrl(course.getCoverUrl());
        courseInfo.setDescription(course.getDescription());
        courseInfo.setShortDescription(course.getShortDescription());
        courseInfo.setSeriesOrder(course.getSeriesOrder());
        courseInfo.setStatus(course.getStatus());

        return courseInfo;
    }

    /**
     * 转换课节信息
     */
    default AppWbClassCourseSeriesDetailRespVO.LessonInfo convertToLessonInfo(WbClassCourseLessonDO lesson) {
        if (lesson == null) {
            return null;
        }

        AppWbClassCourseSeriesDetailRespVO.LessonInfo lessonInfo = new AppWbClassCourseSeriesDetailRespVO.LessonInfo();
        lessonInfo.setId(lesson.getId());
        lessonInfo.setLessonName(lesson.getLessonName());
        lessonInfo.setLessonDescription(lesson.getLessonDescription());
        lessonInfo.setLessonCoverUrl(lesson.getLessonCoverUrl());
        lessonInfo.setSort(lesson.getSort());
        lessonInfo.setExerciseCount(lesson.getExerciseCount());
        lessonInfo.setUnlockType(lesson.getUnlockType());
        lessonInfo.setAutoUnlock(lesson.getAutoUnlock());

        return lessonInfo;
    }

    /**
     * 转换课节信息列表
     */
    default List<AppWbClassCourseSeriesDetailRespVO.LessonInfo> convertToLessonInfoList(List<WbClassCourseLessonDO> lessons) {
        if (CollUtil.isEmpty(lessons)) {
            return new ArrayList<>();
        }

        return lessons.stream()
                .map(this::convertToLessonInfo)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

}
