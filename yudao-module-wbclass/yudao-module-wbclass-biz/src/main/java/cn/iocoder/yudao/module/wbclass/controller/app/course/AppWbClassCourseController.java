package cn.iocoder.yudao.module.wbclass.controller.app.course;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.security.core.annotations.PreAuthenticated;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.module.wbclass.controller.app.course.vo.*;
import cn.iocoder.yudao.module.wbclass.controller.app.vo.AppWbClassUserCourseRespVO;
import cn.iocoder.yudao.module.wbclass.convert.course.WbClassCourseConvert;
import cn.iocoder.yudao.module.wbclass.convert.course.WbClassCourseOrderConvert;
import cn.iocoder.yudao.module.wbclass.convert.course.WbClassCourseSeriesConvert;
import cn.iocoder.yudao.module.wbclass.dal.dataobject.course.WbClassCourseDO;
import cn.iocoder.yudao.module.wbclass.dal.dataobject.course.WbClassCourseOrderDO;
import cn.iocoder.yudao.module.wbclass.service.course.WbClassCourseOrderService;
import cn.iocoder.yudao.module.wbclass.service.course.WbClassCourseUserRelationService;
import cn.iocoder.yudao.module.wbclass.service.courselesson.WbClassCourseLessonService;
import cn.iocoder.yudao.module.wbclass.service.course.WbClassCourseService;
import cn.iocoder.yudao.module.wbclass.controller.admin.courselesson.vo.WbClassCourseLessonRespVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.compress.utils.Lists;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static cn.hutool.extra.servlet.ServletUtil.getClientIP;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Api(tags = "用户App - 练习营课程商城")
@RestController
@RequestMapping("/wbclass/course")
@Validated
public class AppWbClassCourseController {

    @Resource
    private WbClassCourseService courseService;

    @Resource
    private WbClassCourseOrderService orderService;
    @Resource
    private WbClassCourseUserRelationService courseUserRelationService;
    @Resource
    private WbClassCourseLessonService courseLessonService;

    @GetMapping("/page")
    @ApiOperation("获得课程产品分页")
    public CommonResult<PageResult<AppWbClassCourseRespVO>> getCoursePage(@Valid AppWbClassCoursePageReqVO pageVO) {
        PageResult<WbClassCourseDO> pageResult = courseService.getAppCoursePage(pageVO);
        return success(WbClassCourseConvert.INSTANCE.convertAppPage(pageResult));
    }

    @GetMapping("/get")
    @ApiOperation("获得课程产品详情")
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Long.class)
    public CommonResult<AppWbClassCourseRespVO> getCourse(@RequestParam("id") Long id) {
        WbClassCourseDO course = courseService.getCourse(id);
        return success(WbClassCourseConvert.INSTANCE.convertApp(course));
    }

    @PostMapping("/order/create")
    @ApiOperation("创建课程订单")
    @PreAuthenticated
    public CommonResult<Long> createOrder(@Valid @RequestBody AppWbClassCourseOrderCreateReqVO createReqVO,
                                          HttpServletRequest request) {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        String userIp = getClientIP(request);
        return success(orderService.createOrder(createReqVO, userId, userIp));
    }

    @PostMapping("/order/cancel")
    @ApiOperation("取消订单")
    @ApiImplicitParam(name = "id", value = "订单编号", required = true, dataTypeClass = Long.class)
    @PreAuthenticated
    public CommonResult<Boolean> cancelOrder(@RequestParam("id") Long id) {
        orderService.cancelOrder(id, 1); // 用户取消
        return success(true);
    }

    @GetMapping("/order/get")
    @ApiOperation("获得课程订单")
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Long.class)
    @PreAuthenticated
    public CommonResult<AppWbClassCourseOrderRespVO> getOrder(@RequestParam("id") Long id) {
        WbClassCourseOrderDO order = orderService.getOrder(id);
        return success(WbClassCourseOrderConvert.INSTANCE.convertApp(order));
    }

    @GetMapping("/order/page")
    @ApiOperation("获得我的课程订单分页")
    @PreAuthenticated
    public CommonResult<PageResult<AppWbClassCourseOrderRespVO>> getOrderPage(@Valid AppWbClassCourseOrderPageReqVO pageVO) {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        PageResult<WbClassCourseOrderDO> pageResult = orderService.getAppOrderPage(pageVO, userId);
        return success(WbClassCourseOrderConvert.INSTANCE.convertAppPage(pageResult));
    }

    @GetMapping("/my-courses")
    @ApiOperation("获得我的课程列表")
    @PreAuthenticated
    public CommonResult<List<AppWbClassUserCourseRespVO>> getMyCourses() {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        // 使用新的课程用户关联服务获取用户拥有的课程
        List<WbClassCourseDO> courses = courseUserRelationService.getCoursesByUserId(userId);
        // TODO: 需要创建转换器来转换为AppWbClassUserCourseRespVO
        // 这里暂时返回空列表，等待转换器实现
        return success(Lists.newArrayList());
    }

    @GetMapping("/my-course-series")
    @ApiOperation("获得我的课程分类列表")
    @PreAuthenticated
    public CommonResult<List<AppWbClassCourseCategoryRespVO>> getMyCoursesSeries() {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        // 获取按系列分组的课程数据
        Map<String, List<WbClassCourseDO>> groupedCourses = courseUserRelationService.getCoursesByUserIdGroupBySeries(userId);
        // 转换为分类列表
        List<AppWbClassCourseCategoryRespVO> result = WbClassCourseSeriesConvert.INSTANCE.convertToCategoryList(groupedCourses);
        return success(result);
    }

    @GetMapping("/series/detail")
    @ApiOperation("获得课程系列详情")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "seriesId", value = "系列ID", required = true, example = "1", dataTypeClass = Long.class),
        @ApiImplicitParam(name = "courseId", value = "课程ID（可选，指定后该课程将放到第一位并填充lesson数据）", required = false, example = "1", dataTypeClass = Long.class)
    })
    @PreAuthenticated
    public CommonResult<AppWbClassCourseSeriesDetailRespVO> getSeriesDetail(
            @RequestParam("seriesId") Long seriesId,
            @RequestParam(value = "courseId", required = false) Long courseId) {
        Long userId = SecurityFrameworkUtils.getLoginUserId();

        // 获取按系列分组的课程数据
        Map<String, List<WbClassCourseDO>> groupedCourses = courseUserRelationService.getCoursesByUserIdGroupBySeries(userId);

        // 根据seriesId找到对应的系列
        String targetSeriesName = null;
        List<WbClassCourseDO> seriesCourses = null;

        int currentIndex = 1;
        for (Map.Entry<String, List<WbClassCourseDO>> entry : groupedCourses.entrySet()) {
            String seriesName = entry.getKey();
            if (!"individual_courses".equals(seriesName)) {
                if (currentIndex == seriesId.intValue()) {
                    targetSeriesName = seriesName;
                    seriesCourses = entry.getValue();
                    break;
                }
                currentIndex++;
            }
        }

        if (targetSeriesName == null || seriesCourses == null) {
            return success(null);
        }

        // 先按系列内排序进行默认排序
        seriesCourses.sort((c1, c2) -> {
            Integer order1 = c1.getSeriesOrder() != null ? c1.getSeriesOrder() : Integer.MAX_VALUE;
            Integer order2 = c2.getSeriesOrder() != null ? c2.getSeriesOrder() : Integer.MAX_VALUE;
            return order1.compareTo(order2);
        });

        // 如果传入了courseId，需要重新排序课程列表，将指定课程放到第一位
        if (courseId != null) {
            seriesCourses = reorderCoursesByTargetId(seriesCourses, courseId);
        }

        // 转换为系列详情
        AppWbClassCourseSeriesDetailRespVO result = WbClassCourseSeriesConvert.INSTANCE.convertToSeriesDetail(
            targetSeriesName, seriesCourses, seriesId);

        // 只填充第一个课程的lesson详情（如果传入了courseId，则是指定的课程；否则是默认的第一个课程）
        if (result != null && result.getCourses() != null && !result.getCourses().isEmpty()) {
            fillCourseLessons(result.getCourses().get(0));
        }

        return success(result);
    }

    @GetMapping("/individual/{courseId}/detail")
    @ApiOperation("获得单独课程详情")
    @ApiImplicitParam(name = "courseId", value = "课程ID", required = true, example = "1", dataTypeClass = Long.class)
    @PreAuthenticated
    public CommonResult<AppWbClassCourseSeriesDetailRespVO.CourseInfo> getIndividualCourseDetail(@PathVariable("courseId") Long courseId) {
        Long userId = SecurityFrameworkUtils.getLoginUserId();

        // 获取用户的课程
        List<WbClassCourseDO> userCourses = courseUserRelationService.getCoursesByUserId(userId);

        // 找到指定的课程
        WbClassCourseDO targetCourse = userCourses.stream()
            .filter(course -> course.getId().equals(courseId))
            .findFirst()
            .orElse(null);

        if (targetCourse == null) {
            return success(null);
        }

        // 转换为课程信息
        AppWbClassCourseSeriesDetailRespVO.CourseInfo result = WbClassCourseSeriesConvert.INSTANCE.convertToDetailCourseInfo(targetCourse);

        // 填充课程的lesson详情
        if (result != null) {
            fillCourseLessons(result);
        }

        return success(result);
    }

    /**
     * 根据目标课程ID重新排序课程列表，将指定课程放到第一位
     */
    private List<WbClassCourseDO> reorderCoursesByTargetId(List<WbClassCourseDO> courses, Long targetCourseId) {
        if (courses == null || courses.isEmpty() || targetCourseId == null) {
            return courses;
        }

        List<WbClassCourseDO> reorderedCourses = new ArrayList<>();
        WbClassCourseDO targetCourse = null;

        // 找到目标课程
        for (WbClassCourseDO course : courses) {
            if (course.getId().equals(targetCourseId)) {
                targetCourse = course;
                break;
            }
        }

        // 如果找到目标课程，将其放到第一位
        if (targetCourse != null) {
            reorderedCourses.add(targetCourse);
            // 添加其他课程
            for (WbClassCourseDO course : courses) {
                if (!course.getId().equals(targetCourseId)) {
                    reorderedCourses.add(course);
                }
            }
            return reorderedCourses;
        }

        // 如果没找到目标课程，返回原列表
        return courses;
    }

    /**
     * 填充课程的lesson详情
     */
    private void fillCourseLessons(AppWbClassCourseSeriesDetailRespVO.CourseInfo courseInfo) {
        if (courseInfo == null || courseInfo.getId() == null) {
            return;
        }

        // 获取课程的lesson列表
        List<WbClassCourseLessonRespVO> lessonRespVOs = courseLessonService.getCourseLessonsWithExercises(courseInfo.getId());
        if (lessonRespVOs != null && !lessonRespVOs.isEmpty()) {
            // 转换为LessonInfo列表
            List<AppWbClassCourseSeriesDetailRespVO.LessonInfo> lessonInfos = new ArrayList<>();
            for (WbClassCourseLessonRespVO lessonRespVO : lessonRespVOs) {
                AppWbClassCourseSeriesDetailRespVO.LessonInfo lessonInfo = new AppWbClassCourseSeriesDetailRespVO.LessonInfo();
                lessonInfo.setId(lessonRespVO.getId());
                lessonInfo.setLessonName(lessonRespVO.getLessonName());
                lessonInfo.setLessonDescription(lessonRespVO.getLessonDescription());
                lessonInfo.setLessonCoverUrl(lessonRespVO.getLessonCoverUrl());
                lessonInfo.setSort(lessonRespVO.getSort());
                lessonInfo.setExerciseCount(lessonRespVO.getExerciseCount());
                lessonInfo.setUnlockType(lessonRespVO.getUnlockType());
                lessonInfo.setAutoUnlock(lessonRespVO.getAutoUnlock());
                lessonInfos.add(lessonInfo);
            }
            courseInfo.setLessons(lessonInfos);
        }
    }

}
