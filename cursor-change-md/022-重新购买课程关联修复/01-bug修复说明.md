# 重新购买课程关联修复

## 问题描述

用户在以下场景中遇到bug：
1. 用户购买商品A，获得了商品A的订单，并分配了商品A的课程
2. 用户发起退款，商品A对应的课程改为已取消状态
3. 用户现在重新购买商品A，这个时候用户没有拥有课程了

## 问题根因分析

### 1. 数据库表结构问题

`edusys_wbclass_course_user_relation` 表有唯一约束：
```sql
UNIQUE KEY `uk_user_course` (`user_id`, `course_id`, `deleted`)
```

### 2. 业务逻辑问题

在 `WbClassCourseUserRelationServiceImpl.addCourse` 方法中：

1. **用户购买商品A时**：创建课程用户关联记录（状态：VALID）
2. **用户退款时**：将该记录状态改为CANCELLED，但记录仍然存在
3. **用户重新购买时**：`addCourse` 方法尝试创建新记录，但由于唯一约束冲突而失败

### 3. 原有逻辑缺陷

```java
private void addCourse(...) {
    // 检查是否已存在有效的关联关系
    if (relationMapper.existsValidByUserIdAndCourseId(userId, courseId)) {
        log.info("课程用户关联关系已存在，跳过创建: userId={}, courseId={}", userId, courseId);
        return;
    }

    // 直接创建新记录 - 这里会因为唯一约束冲突而失败
    WbClassCourseUserRelationDO relation = WbClassCourseUserRelationDO.builder()...
    relationMapper.insert(relation);
}
```

## 解决方案

### 1. 在 Mapper 中添加查找和重新激活方法

在 `WbClassCourseUserRelationMapper` 中添加：

```java
/**
 * 查找用户和课程的已取消关联关系
 */
default WbClassCourseUserRelationDO selectCancelledByUserIdAndCourseId(Long userId, Long courseId) {
    return selectOne(new LambdaQueryWrapperX<WbClassCourseUserRelationDO>()
            .eq(WbClassCourseUserRelationDO::getUserId, userId)
            .eq(WbClassCourseUserRelationDO::getCourseId, courseId)
            .eq(WbClassCourseUserRelationDO::getStatus, UserCourseStatusEnum.CANCELLED.getStatus())
            .orderByDesc(WbClassCourseUserRelationDO::getId)
            .last("LIMIT 1"));
}

/**
 * 重新激活已取消的课程关联关系
 */
default void reactivateRelation(Long relationId, Integer acquireType, Long orderId, 
                              Long skuId, Long productId, LocalDateTime expireTime, String remark) {
    update(null, new LambdaUpdateWrapper<WbClassCourseUserRelationDO>()
            .eq(WbClassCourseUserRelationDO::getId, relationId)
            .set(WbClassCourseUserRelationDO::getStatus, UserCourseStatusEnum.VALID.getStatus())
            .set(WbClassCourseUserRelationDO::getAcquireType, acquireType)
            .set(WbClassCourseUserRelationDO::getAcquireTime, LocalDateTime.now())
            .set(WbClassCourseUserRelationDO::getOrderId, orderId)
            .set(WbClassCourseUserRelationDO::getSkuId, skuId)
            .set(WbClassCourseUserRelationDO::getProductId, productId)
            .set(WbClassCourseUserRelationDO::getExpireTime, expireTime)
            .set(WbClassCourseUserRelationDO::getRemark, remark));
}
```

### 2. 在 Service 接口中添加重新激活方法

在 `WbClassCourseUserRelationService` 中添加：

```java
/**
 * 重新激活用户的已取消课程（用于重新购买场景）
 */
boolean reactivateCancelledCourse(Long userId, Long courseId, String courseName, Long orderId,
                                Long skuId, Long productId, LocalDateTime expireTime, String remark);
```

### 3. 修改 Service 实现类

修改 `WbClassCourseUserRelationServiceImpl.addCourse` 方法：

```java
private void addCourse(Long userId, Long courseId, String courseName, CourseAcquireTypeEnum acquireType,
                      Long orderId, Long skuId, Long productId, LocalDateTime expireTime, String remark) {
    // 检查是否已存在有效的关联关系
    if (relationMapper.existsValidByUserIdAndCourseId(userId, courseId)) {
        log.info("课程用户关联关系已存在，跳过创建: userId={}, courseId={}", userId, courseId);
        return;
    }

    // 尝试重新激活已取消的关联关系
    if (reactivateCancelledCourse(userId, courseId, courseName, orderId, skuId, productId, expireTime, remark)) {
        log.info("重新激活已取消的课程用户关联关系: userId={}, courseId={}, acquireType={}",
                userId, courseId, acquireType.getName());
        return;
    }

    // 创建新的关联关系
    WbClassCourseUserRelationDO relation = WbClassCourseUserRelationDO.builder()...
    relationMapper.insert(relation);
}
```

添加重新激活方法的实现：

```java
@Override
@Transactional(rollbackFor = Exception.class)
public boolean reactivateCancelledCourse(Long userId, Long courseId, String courseName, Long orderId,
                                       Long skuId, Long productId, LocalDateTime expireTime, String remark) {
    // 查找已取消的关联关系
    WbClassCourseUserRelationDO cancelledRelation = relationMapper.selectCancelledByUserIdAndCourseId(userId, courseId);
    if (cancelledRelation == null) {
        return false;
    }

    // 重新激活关联关系
    relationMapper.reactivateRelation(
            cancelledRelation.getId(),
            CourseAcquireTypeEnum.PURCHASE.getType(), // 重新购买，设置为购买方式
            orderId,
            skuId,
            productId,
            expireTime,
            remark
    );

    log.info("重新激活已取消的课程用户关联关系成功: relationId={}, userId={}, courseId={}, orderId={}",
            cancelledRelation.getId(), userId, courseId, orderId);
    return true;
}
```

## 修复效果

### 修复前的流程
1. 用户购买商品A → 创建课程用户关联（状态：VALID）
2. 用户退款 → 将关联状态改为CANCELLED
3. 用户重新购买 → 尝试创建新记录 → **唯一约束冲突，失败**

### 修复后的流程
1. 用户购买商品A → 创建课程用户关联（状态：VALID）
2. 用户退款 → 将关联状态改为CANCELLED
3. 用户重新购买 → 检查已取消的关联 → **重新激活现有记录，成功**

## 测试验证

### 测试场景
1. 用户首次购买商品A
2. 用户申请退款
3. 用户重新购买商品A
4. 验证用户是否重新拥有课程访问权限

### 验证SQL
```sql
-- 查看用户的课程关联历史
SELECT id, user_id, course_id, status, acquire_type, acquire_time, order_id, sku_id
FROM edusys_wbclass_course_user_relation 
WHERE user_id = ? AND course_id = ?
ORDER BY id DESC;

-- 查看用户当前有效的课程
SELECT c.id, c.name, r.acquire_time, r.order_id
FROM edusys_wbclass_course_user_relation r
JOIN edusys_wbclass_course c ON r.course_id = c.id
WHERE r.user_id = ? 
  AND r.status = 1 
  AND (r.expire_time IS NULL OR r.expire_time > NOW())
  AND r.deleted = 0;
```

## 注意事项

1. **数据一致性**：重新激活时会更新订单ID、SKU ID等信息，确保数据的一致性
2. **日志记录**：所有操作都有详细的日志记录，便于问题排查
3. **事务处理**：重新激活操作使用事务，确保数据的原子性
4. **向后兼容**：修改不影响现有的业务逻辑，保持向后兼容性

## 部署说明

此修复只涉及代码修改，不需要数据库结构变更，可以直接部署。
