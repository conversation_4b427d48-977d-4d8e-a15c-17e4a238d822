# 课程系列详情接口排序修复说明

## 问题描述

在 `/series/detail` 接口中，当传入 `courseId` 参数时，虽然在 `AppWbClassCourseController.getSeriesDetail` 方法中通过 `reorderCoursesByTargetId` 方法将指定课程放到了第一位，但是在后续调用 `WbClassCourseSeriesConvert.convertToSeriesDetail` 方法时，该方法又重新按照 `seriesOrder` 进行了排序，导致之前的排序被覆盖。

## 问题根因

1. **Controller层排序**：`AppWbClassCourseController.getSeriesDetail` 方法中调用 `reorderCoursesByTargetId` 将指定课程放到第一位
2. **Convert层重新排序**：`WbClassCourseSeriesConvert.convertToSeriesDetail` 方法中又按照 `seriesOrder` 重新排序
3. **排序冲突**：Convert层的排序覆盖了Controller层的排序结果

## 修复方案

### 1. 修改 WbClassCourseSeriesConvert.convertToSeriesDetail 方法

**修改前：**
```java
// 按系列内排序排序
courses.sort((c1, c2) -> {
    Integer order1 = c1.getSeriesOrder() != null ? c1.getSeriesOrder() : Integer.MAX_VALUE;
    Integer order2 = c2.getSeriesOrder() != null ? c2.getSeriesOrder() : Integer.MAX_VALUE;
    return order1.compareTo(order2);
});
```

**修改后：**
```java
// 不进行排序，由调用方负责排序
```

### 2. 修改 AppWbClassCourseController.getSeriesDetail 方法

**修改前：**
```java
// 如果传入了courseId，需要重新排序课程列表，将指定课程放到第一位
if (courseId != null) {
    seriesCourses = reorderCoursesByTargetId(seriesCourses, courseId);
}
```

**修改后：**
```java
// 先按系列内排序进行默认排序
seriesCourses.sort((c1, c2) -> {
    Integer order1 = c1.getSeriesOrder() != null ? c1.getSeriesOrder() : Integer.MAX_VALUE;
    Integer order2 = c2.getSeriesOrder() != null ? c2.getSeriesOrder() : Integer.MAX_VALUE;
    return order1.compareTo(order2);
});

// 如果传入了courseId，需要重新排序课程列表，将指定课程放到第一位
if (courseId != null) {
    seriesCourses = reorderCoursesByTargetId(seriesCourses, courseId);
}
```

## 修复效果

1. **职责分离**：Convert层专注于数据转换，不再负责排序逻辑
2. **排序控制**：Controller层完全控制排序逻辑，确保排序结果不被覆盖
3. **功能正确**：
   - 不传 `courseId` 时：课程按照 `seriesOrder` 排序
   - 传入 `courseId` 时：指定课程放到第一位，其他课程保持 `seriesOrder` 排序

## 测试验证

修复后需要验证以下场景：
1. 不传 `courseId` 参数，验证课程按照 `seriesOrder` 正确排序
2. 传入有效的 `courseId` 参数，验证指定课程被放到第一位
3. 传入无效的 `courseId` 参数，验证返回默认排序结果

## 相关文件

- `yudao-module-wbclass/yudao-module-wbclass-biz/src/main/java/cn/iocoder/yudao/module/wbclass/controller/app/course/AppWbClassCourseController.java`
- `yudao-module-wbclass/yudao-module-wbclass-biz/src/main/java/cn/iocoder/yudao/module/wbclass/convert/course/WbClassCourseSeriesConvert.java`
