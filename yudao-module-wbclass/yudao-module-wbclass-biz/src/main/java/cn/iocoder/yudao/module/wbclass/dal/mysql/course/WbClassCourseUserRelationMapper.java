package cn.iocoder.yudao.module.wbclass.dal.mysql.course;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.wbclass.dal.dataobject.course.WbClassCourseUserRelationDO;
import cn.iocoder.yudao.module.wbclass.enums.UserCourseStatusEnum;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

/**
 * 课程用户关联 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface WbClassCourseUserRelationMapper extends BaseMapperX<WbClassCourseUserRelationDO> {

    /**
     * 根据用户ID查询有效的课程关联列表
     *
     * @param userId 用户ID
     * @return 课程关联列表
     */
    default List<WbClassCourseUserRelationDO> selectValidListByUserId(Long userId) {
        return selectList(new LambdaQueryWrapperX<WbClassCourseUserRelationDO>()
                .eq(WbClassCourseUserRelationDO::getUserId, userId)
                .eq(WbClassCourseUserRelationDO::getStatus, UserCourseStatusEnum.VALID.getStatus())
                .and(wrapper -> wrapper
                        .isNull(WbClassCourseUserRelationDO::getExpireTime)
                        .or()
                        .gt(WbClassCourseUserRelationDO::getExpireTime, LocalDateTime.now())
                )
                .orderByDesc(WbClassCourseUserRelationDO::getAcquireTime)
                .orderByDesc(WbClassCourseUserRelationDO::getId));
    }

    /**
     * 根据用户ID列表查询有效的课程关联列表
     *
     * @param userIds 用户ID列表
     * @return 课程关联列表
     */
    default List<WbClassCourseUserRelationDO> selectValidListByUserIds(Collection<Long> userIds) {
        return selectList(new LambdaQueryWrapperX<WbClassCourseUserRelationDO>()
                .in(WbClassCourseUserRelationDO::getUserId, userIds)
                .eq(WbClassCourseUserRelationDO::getStatus, UserCourseStatusEnum.VALID.getStatus())
                .and(wrapper -> wrapper
                        .isNull(WbClassCourseUserRelationDO::getExpireTime)
                        .or()
                        .gt(WbClassCourseUserRelationDO::getExpireTime, LocalDateTime.now())
                )
                .orderByAsc(WbClassCourseUserRelationDO::getUserId)
                .orderByDesc(WbClassCourseUserRelationDO::getAcquireTime)
                .orderByDesc(WbClassCourseUserRelationDO::getId));
    }

    /**
     * 根据课程ID查询有效的用户关联列表
     *
     * @param courseId 课程ID
     * @return 用户关联列表
     */
    default List<WbClassCourseUserRelationDO> selectValidListByCourseId(Long courseId) {
        return selectList(new LambdaQueryWrapperX<WbClassCourseUserRelationDO>()
                .eq(WbClassCourseUserRelationDO::getCourseId, courseId)
                .eq(WbClassCourseUserRelationDO::getStatus, UserCourseStatusEnum.VALID.getStatus())
                .and(wrapper -> wrapper
                        .isNull(WbClassCourseUserRelationDO::getExpireTime)
                        .or()
                        .gt(WbClassCourseUserRelationDO::getExpireTime, LocalDateTime.now())
                )
                .orderByDesc(WbClassCourseUserRelationDO::getAcquireTime)
                .orderByDesc(WbClassCourseUserRelationDO::getId));
    }

    /**
     * 检查用户是否拥有指定课程
     *
     * @param userId 用户ID
     * @param courseId 课程ID
     * @return 是否拥有
     */
    default boolean existsValidByUserIdAndCourseId(Long userId, Long courseId) {
        return selectCount(new LambdaQueryWrapperX<WbClassCourseUserRelationDO>()
                .eq(WbClassCourseUserRelationDO::getUserId, userId)
                .eq(WbClassCourseUserRelationDO::getCourseId, courseId)
                .eq(WbClassCourseUserRelationDO::getStatus, UserCourseStatusEnum.VALID.getStatus())
                .and(wrapper -> wrapper
                        .isNull(WbClassCourseUserRelationDO::getExpireTime)
                        .or()
                        .gt(WbClassCourseUserRelationDO::getExpireTime, LocalDateTime.now())
                )) > 0;
    }

    /**
     * 根据订单ID查询关联记录
     *
     * @param orderId 订单ID
     * @return 关联记录列表
     */
    default List<WbClassCourseUserRelationDO> selectListByOrderId(Long orderId) {
        return selectList(new LambdaQueryWrapperX<WbClassCourseUserRelationDO>()
                .eq(WbClassCourseUserRelationDO::getOrderId, orderId)
                .orderByDesc(WbClassCourseUserRelationDO::getId));
    }

    /**
     * 根据订单ID取消课程关联（软删除）
     *
     * @param orderId 订单ID
     */
    default void cancelByOrderId(Long orderId) {
        update(null, new LambdaUpdateWrapper<WbClassCourseUserRelationDO>()
                .eq(WbClassCourseUserRelationDO::getOrderId, orderId)
                .set(WbClassCourseUserRelationDO::getStatus, UserCourseStatusEnum.CANCELLED.getStatus()));
    }

    /**
     * 更新过期的课程状态
     */
    default void updateExpiredCourses() {
        update(null, new LambdaUpdateWrapper<WbClassCourseUserRelationDO>()
                .eq(WbClassCourseUserRelationDO::getStatus, UserCourseStatusEnum.VALID.getStatus())
                .isNotNull(WbClassCourseUserRelationDO::getExpireTime)
                .lt(WbClassCourseUserRelationDO::getExpireTime, LocalDateTime.now())
                .set(WbClassCourseUserRelationDO::getStatus, UserCourseStatusEnum.EXPIRED.getStatus()));
    }

    /**
     * 查找用户和课程的已取消关联关系
     *
     * @param userId 用户ID
     * @param courseId 课程ID
     * @return 已取消的关联关系，如果不存在返回null
     */
    default WbClassCourseUserRelationDO selectCancelledByUserIdAndCourseId(Long userId, Long courseId) {
        return selectOne(new LambdaQueryWrapperX<WbClassCourseUserRelationDO>()
                .eq(WbClassCourseUserRelationDO::getUserId, userId)
                .eq(WbClassCourseUserRelationDO::getCourseId, courseId)
                .eq(WbClassCourseUserRelationDO::getStatus, UserCourseStatusEnum.CANCELLED.getStatus())
                .orderByDesc(WbClassCourseUserRelationDO::getId)
                .last("LIMIT 1"));
    }

    /**
     * 重新激活已取消的课程关联关系
     *
     * @param relationId 关联关系ID
     * @param acquireType 新的获得方式
     * @param orderId 新的订单ID
     * @param skuId 新的SKU ID
     * @param productId 新的产品ID
     * @param expireTime 新的过期时间
     * @param remark 新的备注
     */
    default void reactivateRelation(Long relationId, Integer acquireType, Long orderId,
                                  Long skuId, Long productId, LocalDateTime expireTime, String remark) {
        update(null, new LambdaUpdateWrapper<WbClassCourseUserRelationDO>()
                .eq(WbClassCourseUserRelationDO::getId, relationId)
                .set(WbClassCourseUserRelationDO::getStatus, UserCourseStatusEnum.VALID.getStatus())
                .set(WbClassCourseUserRelationDO::getAcquireType, acquireType)
                .set(WbClassCourseUserRelationDO::getAcquireTime, LocalDateTime.now())
                .set(WbClassCourseUserRelationDO::getOrderId, orderId)
                .set(WbClassCourseUserRelationDO::getSkuId, skuId)
                .set(WbClassCourseUserRelationDO::getProductId, productId)
                .set(WbClassCourseUserRelationDO::getExpireTime, expireTime)
                .set(WbClassCourseUserRelationDO::getRemark, remark));
    }

}
