-- 重新购买课程关联修复 - 测试验证SQL

-- =============================================
-- 1. 测试数据准备
-- =============================================

-- 假设测试用户ID为 1001，课程ID为 2001，SKU ID为 3001，产品ID为 4001

-- 查看测试用户当前的课程关联状态
SELECT 
    id,
    user_id,
    course_id,
    course_name,
    status,
    CASE status
        WHEN 1 THEN '有效'
        WHEN 2 THEN '已过期'
        WHEN 3 THEN '已取消'
        WHEN 4 THEN '已删除'
        ELSE '未知'
    END as status_name,
    acquire_type,
    CASE acquire_type
        WHEN 1 THEN '购买'
        WHEN 2 THEN '赠送'
        WHEN 3 THEN '活动'
        WHEN 4 THEN '管理员分配'
        WHEN 5 THEN '其他'
        ELSE '未知'
    END as acquire_type_name,
    acquire_time,
    order_id,
    sku_id,
    product_id,
    expire_time,
    remark,
    create_time,
    update_time
FROM edusys_wbclass_course_user_relation 
WHERE user_id = 1001 AND course_id = 2001
ORDER BY id DESC;

-- =============================================
-- 2. 模拟业务场景测试
-- =============================================

-- 场景1：用户首次购买（应该创建新记录）
-- 这个操作通过Java代码执行，这里只是查看结果
-- courseUserRelationService.addCourseByPurchase(1001L, 2001L, "测试课程", 10001L, 3001L, 4001L, null, "首次购买");

-- 验证首次购买后的状态
SELECT '首次购买后状态' as scenario, COUNT(*) as record_count
FROM edusys_wbclass_course_user_relation 
WHERE user_id = 1001 AND course_id = 2001 AND status = 1;

-- 场景2：用户申请退款（应该将状态改为已取消）
-- 这个操作通过Java代码执行：courseUserRelationService.cancelCoursesByOrderId(10001L);

-- 验证退款后的状态
SELECT '退款后状态' as scenario, status, COUNT(*) as record_count
FROM edusys_wbclass_course_user_relation 
WHERE user_id = 1001 AND course_id = 2001
GROUP BY status;

-- 场景3：用户重新购买（应该重新激活已取消的记录）
-- 这个操作通过Java代码执行：courseUserRelationService.addCourseByPurchase(1001L, 2001L, "测试课程", 10002L, 3001L, 4001L, null, "重新购买");

-- 验证重新购买后的状态
SELECT '重新购买后状态' as scenario, status, COUNT(*) as record_count
FROM edusys_wbclass_course_user_relation 
WHERE user_id = 1001 AND course_id = 2001
GROUP BY status;

-- =============================================
-- 3. 详细验证查询
-- =============================================

-- 查看用户课程关联的完整历史记录
SELECT 
    '用户课程关联历史' as description,
    id,
    status,
    CASE status
        WHEN 1 THEN '有效'
        WHEN 2 THEN '已过期'
        WHEN 3 THEN '已取消'
        WHEN 4 THEN '已删除'
        ELSE '未知'
    END as status_name,
    order_id,
    acquire_time,
    update_time,
    remark
FROM edusys_wbclass_course_user_relation 
WHERE user_id = 1001 AND course_id = 2001
ORDER BY id DESC;

-- 验证用户当前有效的课程列表
SELECT 
    '用户当前有效课程' as description,
    c.id as course_id,
    c.name as course_name,
    r.acquire_time,
    r.order_id,
    r.sku_id,
    r.product_id,
    r.expire_time
FROM edusys_wbclass_course_user_relation r
JOIN edusys_wbclass_course c ON r.course_id = c.id
WHERE r.user_id = 1001
  AND r.status = 1 
  AND (r.expire_time IS NULL OR r.expire_time > NOW())
  AND r.deleted = 0
ORDER BY r.acquire_time DESC;

-- 验证订单和课程关联的一致性
SELECT 
    '订单课程关联一致性检查' as description,
    o.id as order_id,
    o.user_id,
    o.status as order_status,
    o.pay_status,
    o.refund_status,
    r.course_id,
    r.status as relation_status,
    CASE 
        WHEN o.refund_status = 3 AND r.status = 3 THEN '一致：已退款-已取消'
        WHEN o.pay_status = 2 AND o.refund_status != 3 AND r.status = 1 THEN '一致：已支付-有效'
        ELSE '不一致'
    END as consistency_check
FROM edusys_wbclass_course_order o
LEFT JOIN edusys_wbclass_course_user_relation r ON o.id = r.order_id
WHERE o.user_id = 1001 AND o.sku_id = 3001
ORDER BY o.id DESC;

-- =============================================
-- 4. 性能验证查询
-- =============================================

-- 检查是否存在重复的有效关联记录（不应该存在）
SELECT 
    '重复有效关联检查' as description,
    user_id,
    course_id,
    COUNT(*) as duplicate_count
FROM edusys_wbclass_course_user_relation
WHERE status = 1 AND deleted = 0
GROUP BY user_id, course_id
HAVING COUNT(*) > 1;

-- 检查唯一约束是否正常工作
SELECT 
    '唯一约束检查' as description,
    user_id,
    course_id,
    deleted,
    COUNT(*) as record_count
FROM edusys_wbclass_course_user_relation
GROUP BY user_id, course_id, deleted
HAVING COUNT(*) > 1;

-- =============================================
-- 5. 清理测试数据（可选）
-- =============================================

-- 注意：以下清理操作仅在测试环境中执行，生产环境请谨慎操作

-- 清理测试用户的课程关联记录
-- DELETE FROM edusys_wbclass_course_user_relation WHERE user_id = 1001 AND course_id = 2001;

-- 清理测试订单记录
-- DELETE FROM edusys_wbclass_course_order WHERE user_id = 1001 AND sku_id = 3001;

-- =============================================
-- 6. 监控查询（用于生产环境监控）
-- =============================================

-- 监控重新激活操作的频率
SELECT 
    DATE(update_time) as date,
    COUNT(*) as reactivation_count
FROM edusys_wbclass_course_user_relation
WHERE status = 1 
  AND acquire_type = 1 -- 购买方式
  AND update_time > create_time -- 表示记录被更新过
  AND DATE(update_time) >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
GROUP BY DATE(update_time)
ORDER BY date DESC;

-- 监控已取消但未重新激活的课程关联
SELECT 
    COUNT(*) as cancelled_not_reactivated_count
FROM edusys_wbclass_course_user_relation
WHERE status = 3 -- 已取消
  AND update_time < DATE_SUB(NOW(), INTERVAL 1 DAY) -- 超过1天未重新激活
  AND deleted = 0;

-- 监控用户重复购买的情况
SELECT 
    user_id,
    course_id,
    COUNT(DISTINCT order_id) as purchase_count,
    MIN(acquire_time) as first_purchase,
    MAX(acquire_time) as last_purchase
FROM edusys_wbclass_course_user_relation
WHERE acquire_type = 1 -- 购买方式
  AND deleted = 0
GROUP BY user_id, course_id
HAVING COUNT(DISTINCT order_id) > 1
ORDER BY purchase_count DESC, last_purchase DESC
LIMIT 10;
